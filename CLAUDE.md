# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Go implementation of the Gemini SRT Translator - a CLI tool that translates subtitle files using AI providers (Gemini, OpenAI). The application can translate SRT files directly or extract and translate subtitles from MKV video files using pure Go code without external dependencies.

## Build and Development Commands

### Building the Application
```bash
# Build for current platform
go build -o gst ./cmd

# Cross-compilation examples
GOOS=windows GOARCH=amd64 go build -o gst.exe ./cmd
GOOS=darwin GOARCH=amd64 go build -o gst-macos ./cmd
GOOS=linux GOARCH=amd64 go build -o gst-linux ./cmd
```

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests for specific package
go test ./pkg/srt -v
```

### Development Setup
```bash
# Install dependencies
go mod tidy

# Run the application directly
go run ./cmd translate -i subtitle.srt -l French
```

### Test Compilation (Per Global CLAUDE.md Instructions)
After modifying any Go code, perform a test compilation:
```bash
go build -o test-output ./cmd && rm test-output
```

## Architecture and Code Structure

### Core Components

1. **CLI Interface (`cmd/main.go`)**
   - Uses cobra for command-line parsing
   - Handles both `translate` and `list-models` subcommands
   - Manages flag parsing and configuration setup
   - Supports interactive model selection and API key input

2. **Configuration (`internal/config/`)**
   - Central configuration struct with all translator settings
   - Handles API key parsing from environment variables (comma-separated)
   - Default values: gemini-2.5-pro model, batch size 300, streaming enabled

3. **Translation Engine (`internal/translator/`)**
   - Main `Translator` struct orchestrates the entire translation process
   - Supports both SRT file translation and MKV subtitle extraction
   - Key features:
     - Progress tracking and resume capability
     - Batch processing with configurable batch sizes
     - Token limit validation to prevent API errors
     - RTL text detection for Arabic/Hebrew languages
     - Multiple API key rotation for quota management
     - Pure Go MKV parsing and subtitle extraction

4. **MKV Processing (`internal/video/`)**
   - Pure Go implementation for MKV subtitle extraction
   - Uses remko/go-mkvparse library for Matroska parsing
   - Automatically selects best English subtitle track (non-SDH preferred)
   - Supports S_TEXT/UTF8 subtitle codecs
   - Temporary file management with automatic cleanup

5. **SRT Processing (`pkg/srt/`)**
   - SRT file parsing and composition
   - Subtitle timing preservation
   - Public package for external use

6. **Supporting Services**
   - `internal/providers/`: AI provider abstraction layer (Gemini, OpenAI)
   - `internal/helpers/`: Gemini API client utilities
   - `internal/logger/`: Progress bars and colored output

### Translation Workflow

1. **Input Validation**: Check files, API keys, and parameters
2. **File Processing**: Extract SRT from MKV files using pure Go parsing or use SRT directly
3. **Model Validation**: Verify selected AI model availability
4. **Token Management**: Get model token limits and validate batch sizes
5. **Batch Translation**: Process subtitles in configurable batches
6. **Progress Tracking**: Save progress for resumable translations
7. **Output Generation**: Write translated SRT with proper formatting
8. **Cleanup**: Remove temporary files (extracted SRT from MKV)

### Key Design Patterns

- **Configuration-driven**: All behavior controlled through `config.Config`
- **Provider abstraction**: Support for multiple AI providers (Gemini, OpenAI)
- **Pure Go implementation**: No external tool dependencies for MKV processing
- **Streaming responses**: Uses AI providers' streaming APIs for real-time progress
- **Batch processing**: Optimizes API usage by processing multiple subtitles together
- **Resume capability**: Saves progress to JSON files for interrupted translations
- **Multi-API key support**: Rotates through multiple keys for higher quotas
- **Smart track selection**: Automatically selects best English subtitle track from MKV files

### API Integration

- Uses Google's official `google.golang.org/genai` client library
- Supports both thinking-enabled and standard models
- Handles structured JSON responses for subtitle objects
- Implements rate limiting for free quota users
- Token counting to prevent request failures

### Error Handling Conventions

- API errors trigger key rotation when multiple keys available
- File validation occurs before processing begins
- Progress is saved after each successful batch
- Cleanup of temporary files (extracted SRT/audio) on completion

## Environment Variables

- `GEMINI_API_KEY`: Comma-separated list of Gemini API keys
- `OPENAI_API_KEY`: Comma-separated list of OpenAI API keys
- `GOOGLE_GEMINI_BASE_URL`: Custom base URL for Gemini API (optional)
- `OPENAI_BASE_URL`: Custom base URL for OpenAI API (optional)

## Dependencies

Main external dependencies:
- `github.com/spf13/cobra`: CLI framework
- `google.golang.org/genai`: Official Gemini AI client
- `github.com/openai/openai-go`: Official OpenAI client
- `github.com/remko/go-mkvparse`: Fast Matroska parser for MKV subtitle extraction
- `golang.org/x/term`: Terminal password input

## Testing Notes

The project includes unit tests for core functionality:
- SRT parsing functionality in `pkg/srt/srt_test.go`
- MKV subtitle extraction in `internal/video/mkv_test.go`

When adding new features, follow the existing pattern of placing tests alongside the code they test.